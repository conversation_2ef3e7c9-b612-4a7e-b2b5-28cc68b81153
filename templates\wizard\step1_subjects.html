{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第一步：考试科目设置</h4>
                </div>
                <div class="card-body">
                    <p class="card-text">请添加本次任务需要安排的所有考试科目、对应的日期和时间段。</p>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step1_subjects') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="subjects_data" name="subjects_data">
                        
                        <div id="subjects-container">
                            <!-- Subjects will be dynamically added here -->
                        </div>

                        <button type="button" id="add-subject-btn" class="btn btn-secondary mt-3">
                            <i class="fas fa-plus"></i> 添加科目
                        </button>
                    </form>
                </div>
                <div class="card-footer d-flex justify-content-end">
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="subject-template">
    <div class="subject-item card mb-3">
        <div class="card-body">
            <div class="row align-items-end">
                <div class="col-md-2">
                    <label class="form-label">课程代码</label>
                    <input type="text" name="subject_code" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">课程名称</label>
                    <input type="text" name="subject_name" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">开始时间</label>
                    <input type="text" name="start_time" class="form-control" placeholder="YYYY/MM/DD HH:MM" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">结束时间</label>
                    <input type="text" name="end_time" class="form-control" placeholder="YYYY/MM/DD HH:MM" required>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger remove-subject-btn w-100">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const wizardData = {{ wizard_data | tojson | safe }};
    let subjects = wizardData.subjects || [];
    const container = $('#subjects-container');
    const template = $('#subject-template').html();

    function renderSubjects() {
        container.empty();
        subjects.forEach((subject, index) => {
            const subjectEl = $(template);
            subjectEl.find('input[name="subject_code"]').val(subject.code);
            subjectEl.find('input[name="subject_name"]').val(subject.name);
            subjectEl.find('input[name="start_time"]').val(subject.start_time);
            subjectEl.find('input[name="end_time"]').val(subject.end_time);
            subjectEl.find('.remove-subject-btn').attr('data-index', index);
            container.append(subjectEl);
        });
    }

    $('#add-subject-btn').click(function() {
        subjects.push({
            code: '',
            name: '',
            start_time: '',
            end_time: ''
        });
        renderSubjects();
    });

    $(document).on('click', '.remove-subject-btn', function() {
        const index = $(this).data('index');
        subjects.splice(index, 1);
        renderSubjects();
    });

    $('#wizard-form').submit(function(e) {
        let isValid = true;
        const collectedSubjects = [];
        $('.subject-item').each(function() {
            const code = $(this).find('input[name="subject_code"]').val().trim();
            const name = $(this).find('input[name="subject_name"]').val().trim();
            const start_time = $(this).find('input[name="start_time"]').val().trim();
            const end_time = $(this).find('input[name="end_time"]').val().trim();

            if (!code || !name || !start_time || !end_time) {
                isValid = false;
            }

            // Basic format validation for datetime
            const dateTimeRegex = /^\d{4}[\/-]\d{1,2}[\/-]\d{1,2} \d{1,2}:\d{2}$/;
            if ((start_time && !dateTimeRegex.test(start_time)) || (end_time && !dateTimeRegex.test(end_time))) {
                isValid = false;
            }

            collectedSubjects.push({ code, name, start_time, end_time });
        });

        if (!isValid) {
            e.preventDefault();
            alert('请填写所有科目的完整信息，并确保时间格式为 YYYY/MM/DD HH:MM 或 YYYY-MM-DD HH:MM。');
            return;
        }

        if (collectedSubjects.length === 0) {
            e.preventDefault();
            alert('请至少设置一个考试科目。');
            return;
        }

        subjects = collectedSubjects;
        $('#subjects_data').val(JSON.stringify(subjects));
    });

    // Initial render
    renderSubjects();
});
</script>
{% endblock %} 