项目文件结构：

1. 核心功能：
   - core/core.py：监考安排算法实现
   - core/Exam_Config.xlsx：考试配置模板
   - core/考试配置处理结果.json：处理后的配置数据

2. Web应用：
   - app.py：主应用文件，包含路由和业务逻辑
   - config.py：应用配置
   - excel_validator.py：Excel文件验证器
   - extended_validator.py：扩展验证器

3. 前端资源：
   - static/：静态资源（CSS、JS、图片等）
   - templates/：HTML模板
   - template-guide/：模板指南

4. 数据和上传：
   - instance/：实例特定数据
   - uploads/：上传文件存储
   - migrations/：数据库迁移脚本

5. 虚拟环境：
   - venv_linux/：Linux环境
   - venv_windows/：Windows环境