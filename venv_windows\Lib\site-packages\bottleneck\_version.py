
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-02-25T01:15:02+0100",
 "dirty": false,
 "error": null,
 "full-revisionid": "bd70f840ac1e0bc220b9bc6bfee6b42ffa57464a",
 "version": "1.3.8"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
