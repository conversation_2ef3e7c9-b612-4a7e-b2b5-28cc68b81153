{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第二步：监考员设置</h4>
                </div>
                <div class="card-body">
                    <p class="card-text">请添加所有监考员，并设置他们的相关监考限制。确保"必/不监考考场"中填写的名称与下一步设置的考场名称一致。</p>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step2_proctors') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="proctors_data" name="proctors_data">
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="proctors-table">
                                <thead>
                                    <tr>
                                        <th style="width: 5%;">序号</th>
                                        <th>监考老师</th>
                                        <th>任教科目</th>
                                        <th>必监考科目</th>
                                        <th>不监考科目</th>
                                        <th>必监考考场</th>
                                        <th>不监考考场</th>
                                        <th style="width: 8%;">场次限制</th>
                                        <th style="width: 5%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Proctors will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>

                        <button type="button" id="add-proctor-btn" class="btn btn-secondary mt-3">
                            <i class="fas fa-plus"></i> 添加监考员
                        </button>
                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step1_subjects') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="proctor-template">
    <tr>
        <td class="proctor-index text-center" style="vertical-align: middle;"></td>
        <td><input type="text" name="proctor_name" class="form-control" placeholder="姓名" required></td>
        <td><input type="text" name="teaching_subject" class="form-control" placeholder="例如：语文"></td>
        <td>
            <select name="required_subjects" class="form-select select2-multiple" multiple>
                {% for subject in wizard_data.get('subjects', []) %}
                    <option value="{{ subject.name }}">{{ subject.name }}</option>
                {% endfor %}
            </select>
        </td>
        <td>
            <select name="unavailable_subjects" class="form-select select2-multiple" multiple>
                {% for subject in wizard_data.get('subjects', []) %}
                    <option value="{{ subject.name }}">{{ subject.name }}</option>
                {% endfor %}
            </select>
        </td>
        <td><input type="text" name="required_rooms" class="form-control" placeholder="逗号分隔, 如: 1考场,2考场"></td>
        <td><input type="text" name="unavailable_rooms" class="form-control" placeholder="逗号分隔, 如: 3考场"></td>
        <td><input type="number" name="session_limit" class="form-control" min="0" placeholder="数字"></td>
        <td style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm remove-proctor-btn"><i class="fas fa-trash"></i></button></td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const wizardData = {{ wizard_data | tojson | safe }};
    let proctors = wizardData.proctors || [];
    const tableBody = $('#proctors-table tbody');
    const template = $('#proctor-template').html();

    function collectDataFromDOM() {
        const collectedProctors = [];
        $('#proctors-table tbody tr').each(function() {
            const name = $(this).find('input[name="proctor_name"]').val().trim();
            collectedProctors.push({
                name: name,
                teaching_subject: $(this).find('input[name="teaching_subject"]').val().trim(),
                required_subjects: $(this).find('select[name="required_subjects"]').val() || [],
                unavailable_subjects: $(this).find('select[name="unavailable_subjects"]').val() || [],
                required_rooms: $(this).find('input[name="required_rooms"]').val().trim(),
                unavailable_rooms: $(this).find('input[name="unavailable_rooms"]').val().trim(),
                session_limit: $(this).find('input[name="session_limit"]').val()
            });
        });
        return collectedProctors;
    }

    function renderProctors() {
        tableBody.empty();
        proctors.forEach((proctor, index) => {
            const proctorEl = $(template);
            proctorEl.find('.proctor-index').text(index + 1);
            proctorEl.find('input[name="proctor_name"]').val(proctor.name || '');
            proctorEl.find('input[name="teaching_subject"]').val(proctor.teaching_subject || '');
            proctorEl.find('select[name="required_subjects"]').val(proctor.required_subjects || []);
            proctorEl.find('select[name="unavailable_subjects"]').val(proctor.unavailable_subjects || []);
            proctorEl.find('input[name="required_rooms"]').val(proctor.required_rooms || '');
            proctorEl.find('input[name="unavailable_rooms"]').val(proctor.unavailable_rooms || '');
            proctorEl.find('input[name="session_limit"]').val(proctor.session_limit || '');
            
            tableBody.append(proctorEl);

            // Initialize Select2 on the new row
            proctorEl.find('.select2-multiple').select2({
                theme: "bootstrap-5",
                placeholder: "点击选择科目",
                closeOnSelect: false,
                allowClear: true
            });
        });
    }

    function initializeSelect2ForRow(row) {
        $(row).find('.select2-multiple').select2({
            theme: "bootstrap-5",
            placeholder: "点击选择科目",
            closeOnSelect: false,
            allowClear: true
        });
    }

    $('#add-proctor-btn').click(function() {
        // Collect current data before adding a new row to prevent data loss
        proctors = collectDataFromDOM();
        proctors.push({});
        renderProctors();
    });

    $(document).on('click', '.remove-proctor-btn', function() {
        // Destroy select2 instance before removing the row to prevent issues
        $(this).closest('tr').find('.select2-multiple').select2('destroy');
        
        proctors = collectDataFromDOM();
        const index = $(this).closest('tr').index();
        proctors.splice(index, 1);
        renderProctors();
    });

    $(document).on('click', '.clear-selection-btn', function() {
        $(this).siblings('select').val([]).trigger('change');
    });

    $('#wizard-form').submit(function(e) {
        proctors = collectDataFromDOM();
        let isValid = true;

        if (proctors.length > 0) {
            const name = proctors[0].name;
            if (!name) {
                 // Check if it's the only empty row
                if (proctors.length === 1) {
                    proctors = []; // Submit empty if the only row is empty
                } else {
                    isValid = false;
                }
            }
        }
        
        // Final check for any empty names if more than one proctor
        if (proctors.length > 0) {
            isValid = !proctors.some(p => !p.name);
        }

        if (!isValid) {
            e.preventDefault();
            alert('"监考老师"的姓名不能为空。');
            return;
        }

        if (proctors.length === 0) {
            e.preventDefault();
            alert('请至少添加一名监考员。');
            return;
        }
        
        $('#proctors_data').val(JSON.stringify(proctors));
    });

    // Initial render
    if (proctors.length === 0) {
        proctors.push({}); // Start with one empty row
    }
    renderProctors();
});
</script>
{% endblock %}
