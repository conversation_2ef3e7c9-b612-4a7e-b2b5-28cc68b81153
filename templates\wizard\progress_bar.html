<div class="progress-container mb-4">
    {% set steps = [
        ('wizard_step1_subjects', '科目设置'),
        ('wizard_step2_rooms', '考场设置'),
        ('wizard_step3_proctors', '监考员设置'),
        ('wizard_step4_review', '预览生成')
    ] %}
    <div class="progress-bar-wizard d-flex justify-content-between">
        {% for route, label in steps %}
            {% set loop_index = loop.index %}
            {% set is_active = (current_step == loop_index) %}
            {% set is_completed = (current_step > loop_index) %}
            <div class="progress-step {% if is_active %}active{% elif is_completed %}completed{% endif %}">
                <div class="progress-step-circle">
                    {% if is_completed %}<i class="fas fa-check"></i>{% else %}{{ loop_index }}{% endif %}
                </div>
                <div class="progress-step-label">{{ label }}</div>
            </div>
            {% if not loop.last %}
            <div class="progress-bar-connector {% if is_completed %}completed{% endif %}"></div>
            {% endif %}
        {% endfor %}
    </div>
</div>

<style>
.progress-container {
    width: 100%;
    margin-bottom: 2rem;
}
.progress-bar-wizard {
    align-items: center;
}
.progress-step {
    text-align: center;
    position: relative;
    flex: 1 1 0;
}
.progress-step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #fff;
    line-height: 30px;
    font-weight: bold;
    margin: 0 auto 10px auto;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}
.progress-step-label {
    font-size: 14px;
    color: #9e9e9e;
    transition: all 0.3s ease;
}
.progress-step.completed .progress-step-circle,
.progress-step.active .progress-step-circle {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
.progress-step.active .progress-step-label {
    color: #0d6efd;
    font-weight: bold;
}
.progress-step.completed .progress-step-label {
    color: #212529;
}
.progress-bar-connector {
    height: 2px;
    background-color: #e0e0e0;
    flex-grow: 1;
    margin: 0 -15px; /* Overlap with circles */
    position: relative;
    top: -20px; /* Align with middle of circle */
    transition: background-color 0.3s ease;
}
.progress-bar-connector.completed {
    background-color: #0d6efd;
}
</style> 