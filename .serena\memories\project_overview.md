这是一个监考安排系统，主要功能包括：

1. 用户管理：
   - 支持用户注册、登录、权限管理
   - 用户角色：普通用户、VIP用户、管理员

2. 任务管理：
   - 支持上传Excel文件进行监考安排
   - 任务队列和优先级处理
   - 文件验证和处理
   - 结果下载

3. 核心功能：
   - 基于OR-Tools的监考安排优化算法
   - 支持多种约束条件（时间、场次、科目等）
   - 支持不同的监考时间策略（集中/分散）

4. 系统特性：
   - Redis缓存支持
   - 多线程任务处理
   - 文件锁和并发控制
   - CSRF保护
   - 日志记录

技术栈：
- 后端：Flask + SQLAlchemy + Redis
- 前端：Bootstrap + jQuery
- 数据处理：Pandas + OR-Tools
- 数据存储：SQLite