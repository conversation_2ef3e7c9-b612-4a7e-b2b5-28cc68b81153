常用命令：

1. **环境设置**
```bash
# 安装依赖
pip install ortools pandas numpy openpyxl xlrd XlsxWriter

# 检查Python版本
python --version  # 需要3.8+
```

2. **运行程序**
```bash
# 直接运行主程序
python main.py

# 如果需要指定配置文件路径
python main.py --config path/to/Exam_Config.xlsx
```

3. **文件操作**
```bash
# Windows系统下
dir  # 列出目录内容
type filename  # 查看文件内容
copy source destination  # 复制文件
move source destination  # 移动文件
del filename  # 删除文件
```

4. **Git操作**
```bash
git status  # 查看状态
git add .  # 添加所有更改
git commit -m "message"  # 提交更改
git push  # 推送到远程
```