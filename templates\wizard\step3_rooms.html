{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding: 0;
        list-style: none;
        position: relative;
    }
    .wizard-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }
    .wizard-step {
        flex: 1;
        text-align: center;
        position: relative;
        z-index: 2;
    }
    .wizard-step-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #6c757d;
    }
    .wizard-step.active .wizard-step-link,
    .wizard-step.completed .wizard-step-link {
        color: #0d6efd;
    }
    .wizard-step-indicator {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .wizard-step.active .wizard-step-indicator {
        background-color: #0d6efd;
        color: #fff;
        border-color: #0d6efd;
    }
    .wizard-step.completed .wizard-step-indicator {
        background-color: #fff;
        color: #0d6efd;
        border-color: #0d6efd;
    }
    .wizard-step-label {
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    .table-responsive {
        max-height: 60vh;
        overflow: auto;
    }
    thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 3;
    }
    tbody th {
        position: sticky;
        left: 0;
        background-color: #f8f9fa;
        z-index: 2;
    }
    thead th:first-child {
        left: 0;
        z-index: 4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            {% include 'wizard/progress_bar.html' %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">向导第三步：考场与需求设置</h4>
                </div>
                <div class="card-body">
                    <p class="card-text">请设置考场，并为每个考场分配不同科目所需的监考员数量。</p>
                    <form id="wizard-form" method="POST" action="{{ url_for('wizard_step3_rooms') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="rooms_data" name="rooms_data">
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="rooms-table">
                                <thead>
                                    <tr>
                                        <th style="width: 20%;">考场名称</th>
                                        {% for subject in wizard_data.get('subjects', []) %}
                                        <th>{{ subject.name }} (监考人数)</th>
                                        {% endfor %}
                                        <th style="width: 5%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rooms will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>

                        <button type="button" id="add-room-btn" class="btn btn-secondary mt-3">
                            <i class="fas fa-plus"></i> 添加考场
                        </button>
                    </form>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <a href="{{ url_for('wizard_step2_proctors') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </a>
                    <button type="submit" form="wizard-form" class="btn btn-primary">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<template id="room-template">
    <tr>
        <td><input type="text" name="room_name" class="form-control" required></td>
        {% for subject in wizard_data.get('subjects', []) %}
        <td><input type="number" name="demand_{{ subject.name }}" class="form-control" min="0" placeholder="数量"></td>
        {% endfor %}
        <td style="vertical-align: middle;"><button type="button" class="btn btn-danger btn-sm remove-room-btn"><i class="fas fa-trash"></i></button></td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const wizardData = {{ wizard_data | tojson | safe }};
    let rooms = wizardData.rooms || [];
    const subjects = wizardData.subjects || [];
    const tableBody = $('#rooms-table tbody');

    function collectDataFromDOM() {
        const collectedRooms = [];
        $('#rooms-table tbody tr').each(function() {
            const roomName = $(this).find('input[name="room_name"]').val().trim();
            const demands = {};
            $(this).find('input[type="number"]').each(function() {
                const subjectName = $(this).attr('name').replace('demand_', '');
                demands[subjectName] = $(this).val();
            });
            collectedRooms.push({
                name: roomName,
                demands: demands
            });
        });
        return collectedRooms;
    }

    function renderRooms() {
        tableBody.empty();
        rooms.forEach(room => {
            const roomEl = $('#room-template').html();
            const $roomEl = $(roomEl);
            $roomEl.find('input[name="room_name"]').val(room.name || '');
            
            subjects.forEach(subject => {
                const demandValue = (room.demands && room.demands[subject.name]) ? room.demands[subject.name] : '';
                $roomEl.find(`input[name="demand_${subject.name}"]`).val(demandValue);
            });

            tableBody.append($roomEl);
        });
    }

    $('#add-room-btn').click(function() {
        rooms = collectDataFromDOM();
        const nextRoomNumber = rooms.length + 1;
        rooms.push({ name: `${nextRoomNumber}考场`, demands: {} });
        renderRooms();
    });

    $(document).on('click', '.remove-room-btn', function() {
        rooms = collectDataFromDOM();
        const index = $(this).closest('tr').index();
        rooms.splice(index, 1);
        renderRooms();
    });

    $('#wizard-form').submit(function(e) {
        const finalRooms = collectDataFromDOM().filter(r => r.name); // Filter out empty rows
        
        if (finalRooms.length === 0) {
            e.preventDefault();
            alert('请至少设置一个考场。');
            return;
        }

        $('#rooms_data').val(JSON.stringify(finalRooms));
    });

    // Initial render
    renderRooms();
});
</script>
{% endblock %}